import 'package:flutter_test/flutter_test.dart';
import 'package:gp_feat_ticket/constants/url/ticket/ticket_whitelist_workspace.dart';

void main() {
  group('TicketWhitelistWorkspace', () {
    test('should return true for whitelisted workspace IDs', () {
      // F88
      expect(TicketWhitelistWorkspace.isWorkspaceInWhitelist('523854436178539'), true);
      
      // Gapo Prod
      expect(TicketWhitelistWorkspace.isWorkspaceInWhitelist('523866125265220'), true);
      
      // Gapo Staging
      expect(TicketWhitelistWorkspace.isWorkspaceInWhitelist('581860791816317'), true);
    });

    test('should return false for non-whitelisted workspace IDs', () {
      expect(TicketWhitelistWorkspace.isWorkspaceInWhitelist('123456789'), false);
      expect(TicketWhitelistWorkspace.isWorkspaceInWhitelist('999999999'), false);
    });

    test('should return false for null or empty workspace IDs', () {
      expect(TicketWhitelistWorkspace.isWorkspaceInWhitelist(null), false);
      expect(TicketWhitelistWorkspace.isWorkspaceInWhitelist(''), false);
    });
  });
}
