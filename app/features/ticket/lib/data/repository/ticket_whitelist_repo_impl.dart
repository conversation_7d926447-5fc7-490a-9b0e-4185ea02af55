/*
 * Created Date: Sunday, 11th August 2025, 16:00:00
 * Author: gapo
 * -----
 * Last Modified: Sunday, 11th August 2025 16:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core/configs/constants.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../constants/url/ticket/ticket_whitelist_workspace.dart';
import '../../domain/domain.dart';
import '../data.dart';

@LazySingleton(
    as: TicketWhitelistRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kTicketWhitelistRepository')
final class TicketWhitelistRepositoryImpl implements TicketWhitelistRepository {
  const TicketWhitelistRepositoryImpl(
    @Named('kTicketService') this._ticketService,
    @Named('kTicketEsService') this._ticketEsService,
  );

  final TicketService _ticketService;
  final TicketEsService _ticketEsService;

  @override
  Future<ListAPIResponseV2<TicketListResponse>> tickets(
    TicketListParams params,
  ) async {
    final currentWorkspaceId = Constants.workspaceId();

    if (TicketWhitelistWorkspace.isWorkspaceInWhitelist(currentWorkspaceId)) {
      // Gọi ES API nếu workspace trong whitelist
      return _ticketEsService.tickets(params: params);
    } else {
      // Gọi API thường nếu workspace không trong whitelist
      return _ticketService.tickets(params: params);
    }
  }

  @override
  Future<ListAPIResponseV2<TicketListResponse>> relativeTickets(
    TicketListRelativeParams params,
  ) async {
    final currentWorkspaceId = Constants.workspaceId();

    if (TicketWhitelistWorkspace.isWorkspaceInWhitelist(currentWorkspaceId)) {
      // Gọi ES API nếu workspace trong whitelist
      return _ticketEsService.relativeTickets(params: params);
    } else {
      // Gọi API thường nếu workspace không trong whitelist
      return _ticketService.relativeTickets(params: params);
    }
  }

  @override
  Future<ApiResponseV2<TicketFlowChartResponse>> flowCharts({
    required String ticketId,
  }) async {
    final currentWorkspaceId = Constants.workspaceId();

    if (TicketWhitelistWorkspace.isWorkspaceInWhitelist(currentWorkspaceId)) {
      // Gọi ES API nếu workspace trong whitelist
      return _ticketEsService.flowCharts(ticketId: ticketId);
    } else {
      // Gọi API thường nếu workspace không trong whitelist
      return _ticketService.flowCharts(ticketId: ticketId);
    }
  }
}
