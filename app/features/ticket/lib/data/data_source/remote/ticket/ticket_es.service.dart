/*
 * Created Date: Sunday, 11th August 2025, 16:00:00
 * Author: gapo
 * -----
 * Last Modified: Sunday, 11th August 2025 16:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

import '../../../../constants/url/ticket/ticket_url.constants.dart';
import '../../../model/model.dart';

part 'ticket_es.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kTicketEsService')
@RestApi()
abstract class TicketEsService {
  @FactoryMethod()
  factory TicketEsService(
    @Named('kDio') Dio dio, {
    @Named('kTicketUrl') String? baseUrl,
  }) = _TicketEsService;

  /// Lấy danh sách tickets từ ES API
  @GET(TicketUrlConstants.kTicketEsUrl)
  Future<ListAPIResponseV2<TicketListResponse>> tickets({
    @Queries() required TicketListParams params,
  });

  /// Lấy danh sách relative tickets từ ES API
  @GET(TicketUrlConstants.kTicketEsUrl)
  Future<ListAPIResponseV2<TicketListResponse>> relativeTickets({
    @Queries() required TicketListRelativeParams params,
  });

  /// Lấy flowchart từ ES API
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kFlowChartEsUrl}')
  Future<ApiResponseV2<TicketFlowChartResponse>> flowCharts({
    @Path('ticketId') required String ticketId,
  });
}
