// ignore_for_file: public_member_api_docs

import 'package:gp_core/configs/constants.dart';
import 'ticket_url.constants.dart';

final class TicketWhitelistWorkspace {
  /// Danh sách whitelist workspace ID để sử dụng ES API
  static const List<String> _whitelistWorkspaceIds = [
    // F88
    '523854436178539',
    // Gapo Prod
    '523866125265220',
    // Gapo Staging
    '581860791816317',
    // Gapo Uat
    '523866125265220',
  ];

  /// Kiểm tra workspace ID có trong whitelist không
  static bool isWorkspaceInWhitelist(String? workspaceId) {
    if (workspaceId == null || workspaceId.isEmpty) {
      return false;
    }
    return _whitelistWorkspaceIds.contains(workspaceId);
  }
}
