# Ticket Whitelist Repository

## <PERSON><PERSON> tả

`TicketWhitelistRepository` là một repository được thiết kế để xử lý logic whitelist workspace cho các API ticket. Repository này sẽ tự động quyết định gọi đến ES API hoặc API thường dựa trên việc workspace ID hiện tại có nằm trong whitelist hay không.

## Cách hoạt động

1. **Kiểm tra whitelist**: Repository sử dụng `TicketWhitelistWorkspace.isWorkspaceInWhitelist()` để kiểm tra workspace ID hiện tại
2. **Gọi API tương ứng**:
   - Nếu workspace **trong whitelist** → gọi **ES API** (`TicketEsService`)
   - Nếu workspace **không trong whitelist** → gọi **API thường** (`TicketService`)

## Các method được hỗ trợ

### 1. `tickets(TicketListParams params)`
- Lấy danh sách tickets
- ES API: `/tickets/es`
- API thường: `/tickets/es` (hiện tại cả hai đều dùng ES endpoint)

### 2. `relativeTickets(TicketListRelativeParams params)`
- Lấy danh sách relative tickets
- ES API: `/tickets/es`
- API thường: `/tickets/es`

### 3. `flowCharts(String ticketId)`
- Lấy flowchart của ticket
- ES API: `/tickets/{ticketId}/full-flowcharts/es`
- API thường: `/tickets/{ticketId}/full-flowcharts/es`

## Whitelist workspace IDs

Danh sách workspace IDs được whitelist (định nghĩa trong `TicketWhitelistWorkspace`):

- **F88**: `523854436178539`
- **Gapo Prod**: `523866125265220`
- **Gapo Staging**: `581860791816317`
- **Gapo UAT**: `523866125265220`

## Cách sử dụng

Repository này đã được tích hợp vào các UseCase:

- `TicketListUseCase`
- `TicketRelativeUseCase`
- `TicketFlowChartUseCase`

Các UseCase này sẽ tự động sử dụng `TicketWhitelistRepository` thay vì `TicketRepository` trực tiếp.

## Dependency Injection

Repository được đăng ký với tên `kTicketWhitelistRepository`:

```dart
@LazySingleton(as: TicketWhitelistRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kTicketWhitelistRepository')
final class TicketWhitelistRepositoryImpl implements TicketWhitelistRepository
```

## Testing

Có thể test logic whitelist bằng cách chạy:

```bash
flutter test test/constants/ticket_whitelist_workspace_test.dart
```
