/*
 * Created Date: Sunday, 11th August 2025, 16:00:00
 * Author: gapo
 * -----
 * Last Modified: Sunday, 11th August 2025 16:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';

import '../../data/data.dart';

/// Repository để xử lý logic whitelist workspace cho ticket API
abstract class TicketWhitelistRepository {
  /// Lấy danh sách tickets với logic whitelist
  /// Nếu workspace trong whitelist thì gọi ES API, ngược lại gọi API thường
  Future<ListAPIResponseV2<TicketListResponse>> tickets(
    TicketListParams params,
  );

  /// Lấy danh sách relative tickets với logic whitelist
  /// Nếu workspace trong whitelist thì gọi ES API, ngược lại gọi API thường
  Future<ListAPIResponseV2<TicketListResponse>> relativeTickets(
    TicketListRelativeParams params,
  );

  /// Lấy flowchart với logic whitelist
  /// Nếu workspace trong whitelist thì gọi ES API, ngược lại gọi API thường
  Future<ApiResponseV2<TicketFlowChartResponse>> flowCharts({
    required String ticketId,
  });
}
