// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/data.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketFlowChartUseCase extends GPBaseFutureUseCase<TicketFlowChartInput,
    ApiResponseV2<TicketFlowChartResponse>> {
  TicketFlowChartUseCase(
    @Named('kTicketWhitelistRepository') this._ticketWhitelistRepository,
  );

  final TicketWhitelistRepository _ticketWhitelistRepository;

  @override
  Future<ApiResponseV2<TicketFlowChartResponse>> buildUseCase(
    TicketFlowChartInput input,
  ) async {
    return _ticketWhitelistRepository.flowCharts(
      ticketId: input.ticketId,
    );
  }
}

class TicketFlowChartInput extends GPBaseInput {
  const TicketFlowChartInput({
    required this.ticketId,
  });

  final String ticketId;
}
